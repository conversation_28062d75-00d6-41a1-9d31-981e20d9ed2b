{"name": "dento", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build", "start": "next start", "lint": "next lint", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "test:ci": "jest --ci --coverage --watchAll=false"}, "dependencies": {"@radix-ui/react-popover": "^1.1.14", "@radix-ui/react-select": "^2.2.5", "@radix-ui/react-slot": "^1.2.3", "chart.js": "^4.5.0", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "daisyui": "^5.0.43", "date-fns": "^4.1.0", "firebase": "^11.9.1", "firebase-admin": "^13.4.0", "framer-motion": "^12.19.2", "lucide-react": "^0.523.0", "next": "15.3.4", "next-themes": "^0.4.6", "react": "^19.0.0", "react-chartjs-2": "^5.3.0", "react-datepicker": "^8.4.0", "react-day-picker": "^9.7.0", "react-dom": "^19.0.0", "react-phone-input-2": "^2.15.1", "react-phone-number-input": "^3.4.12", "sonner": "^2.0.6", "tailwind-merge": "^3.3.1", "zod": "^3.25.76", "zustand": "^5.0.6"}, "devDependencies": {"@eslint/eslintrc": "^3", "@tailwindcss/postcss": "^4", "@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^16.3.0", "@testing-library/user-event": "^14.6.1", "@types/jest": "^30.0.0", "@types/node": "^20", "@types/react": "^19", "@types/react-datepicker": "^6.2.0", "@types/react-dom": "^19", "eslint": "^9", "eslint-config-next": "15.3.4", "jest": "^30.0.4", "jest-environment-jsdom": "^30.0.4", "tailwindcss": "^4", "tw-animate-css": "^1.3.4", "typescript": "^5"}}