
/**
 * Type definitions for the Dento application
 * Centralized type definitions for better maintainability
 */

// ============================================================================
// USER TYPES
// ============================================================================

export interface PendingUser {
  id: string;
  firstName: string;
  lastName: string;
  email: string;
  phone: string;
  status: 'pending' | 'approved' | 'rejected';
  createdAt: Date;
  updatedAt: Date;
}

export interface User {
  id: string;
  firstName: string;
  lastName: string;
  email: string;
  phone: string;
  address: string;
  birthDate: Date;
  role: 'doctor' | 'assistant';
  clinic: string;
  createdAt: Date;
  updatedAt: Date;
}

// ============================================================================
// PATIENT TYPES
// ============================================================================

export interface Patient {
  id: string;
  doctorId: string;
  firstName: string;
  lastName: string;
  email?: string;
  tcKimlik: string;
  phone: string;
  gender: string;
  birthDate: Date;
  yas?: number;
  address?: string;
  totalProcedures?: number;
  createdAt: Date;
  updatedAt: Date;
}

// ============================================================================
// REQUEST FORM TYPES
// ============================================================================

export interface RequestFormData {
  id?: string;
  patientId: string;
  userId: string;
  diagnosis: string;
  notes: string;
  images?: string[];
  xrayTypes: string[];
  bitewingSides: string[];
  selectedTeeth?: string;
  paymentResponsible: 'clinic' | 'patient';
  priorityStatus: 'Normal' | 'Acil' | 'Çok Acil' | 'Düşük';
  status: 'pending' | 'completed' | 'draft';
  createdAt: Date;
  completedAt?: Date;
}

// Backward compatibility alias
export type IstemFormuData = RequestFormData;

// ============================================================================
// UTILITY TYPES
// ============================================================================

export type UserRole = 'doctor' | 'assistant' | 'admin';
export type UserStatus = 'pending' | 'approved' | 'rejected';
export type FormStatus = 'pending' | 'completed' | 'draft';
export type PriorityStatus = 'Normal' | 'Acil' | 'Çok Acil' | 'Düşük';
export type PaymentResponsible = 'clinic' | 'patient';

// ============================================================================
// API RESPONSE TYPES
// ============================================================================

export interface ApiResponse<T = any> {
  success: boolean;
  data?: T;
  error?: string;
  message?: string;
}

export interface PaginatedResponse<T> {
  data: T[];
  total: number;
  page: number;
  limit: number;
  hasMore: boolean;
}

// ============================================================================
// FORM TYPES
// ============================================================================

export interface FormField {
  name: string;
  label: string;
  type: 'text' | 'email' | 'password' | 'tel' | 'date' | 'select' | 'textarea';
  required?: boolean;
  placeholder?: string;
  options?: { value: string; label: string }[];
}

export interface ValidationError {
  field: string;
  message: string;
}

// ============================================================================
// STATISTICS TYPES
// ============================================================================

export interface PatientStats {
  totalPatients: number;
  newPatientsThisMonth: number;
  averageAge: number;
  genderDistribution: {
    male: number;
    female: number;
  };
}

export interface FormStats {
  totalForms: number;
  pendingForms: number;
  completedForms: number;
  draftForms: number;
  formsThisMonth: number;
}

export interface UserStats {
  totalUsers: number;
  activeUsers: number;
  pendingUsers: number;
  usersByRole: {
    doctor: number;
    assistant: number;
  };
}

// ============================================================================
// COMPONENT PROPS TYPES
// ============================================================================

export interface BaseComponentProps {
  className?: string;
  children?: React.ReactNode;
}

export interface TableColumn<T = any> {
  key: keyof T | string;
  label: string;
  sortable?: boolean;
  render?: (value: any, row: T) => React.ReactNode;
}

export interface ModalProps extends BaseComponentProps {
  isOpen: boolean;
  onClose: () => void;
  title?: string;
  size?: 'sm' | 'md' | 'lg' | 'xl';
}

// ============================================================================
// NAVIGATION TYPES
// ============================================================================

export interface NavItem {
  label: string;
  href: string;
  icon?: React.ComponentType;
  children?: NavItem[];
  roles?: UserRole[];
}

export interface BreadcrumbItem {
  label: string;
  href?: string;
}

// ============================================================================
// FILE TYPES
// ============================================================================

export interface FileUpload {
  file: File;
  preview?: string;
  progress?: number;
  error?: string;
}

export interface UploadedFile {
  id: string;
  name: string;
  url: string;
  size: number;
  type: string;
  uploadedAt: Date;
}