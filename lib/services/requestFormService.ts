import { db } from '../firebase';
import {
  collection,
  addDoc,
  getDocs,
  doc,
  updateDoc,
  deleteDoc,
  query,
  where,
  orderBy,
} from 'firebase/firestore';
import { RequestFormData } from '../types';

export const saveRequestForm = async (requestFormData: Omit<RequestFormData, 'id' | 'createdAt' | 'status'>, userId: string): Promise<string> => {
  try {
    const requestFormCollection = collection(db, 'users', userId, 'istem-formu');

    const processedData: Omit<RequestFormData, 'id'> = {
      ...requestFormData,
      status: 'pending',
      createdAt: new Date(),
    };

    const docRef = await addDoc(requestFormCollection, processedData);
    return docRef.id;
  } catch (error) {
    console.error('Error saving request form: ', error);
    throw new Error('Failed to save request form');
  }
};

// Backward compatibility alias
export const saveIstemFormu = saveRequestForm;

/**
 * Get waiting request form records for a specific patient
 */
export const getWaitingRequestFormsByPatient = async (patientId: string): Promise<RequestFormData[]> => {
  try {
    const allForms: RequestFormData[] = [];

    // Get all users to check their request-form collections
    const usersSnapshot = await getDocs(collection(db, 'users'));

    for (const userDoc of usersSnapshot.docs) {
      const requestFormCollection = collection(db, 'users', userDoc.id, 'istem-formu');
      const waitingQuery = query(
        requestFormCollection,
        where('patientId', '==', patientId),
        where('status', '==', 'pending'),
        orderBy('createdAt', 'desc')
      );

      const querySnapshot = await getDocs(waitingQuery);
      const userForms = querySnapshot.docs.map(doc => ({
        id: doc.id,
        ...doc.data(),
        userId: userDoc.id, // Add the user ID for later use
        createdAt: doc.data().createdAt?.toDate() || new Date()
      })) as RequestFormData[];

      allForms.push(...userForms);
    }

    // Sort by creation date (newest first)
    return allForms.sort((a, b) => b.createdAt.getTime() - a.createdAt.getTime());
  } catch (error) {
    console.error('Error fetching waiting request forms: ', error);
    throw new Error('Failed to fetch waiting request forms');
  }
};

// Backward compatibility alias
export const getWaitingIstemFormuByPatient = getWaitingRequestFormsByPatient;

/**
 * Get completed request form records for a specific patient
 */
export const getCompletedRequestFormsByPatient = async (patientId: string): Promise<RequestFormData[]> => {
  try {
    const allForms: RequestFormData[] = [];

    const usersSnapshot = await getDocs(collection(db, 'users'));

    for (const userDoc of usersSnapshot.docs) {
      const requestFormCollection = collection(db, 'users', userDoc.id, 'istem-formu');
      const completedQuery = query(
        requestFormCollection,
        where('patientId', '==', patientId),
        where('status', '==', 'completed'),
        orderBy('createdAt', 'desc')
      );

      const querySnapshot = await getDocs(completedQuery);
      const userForms = querySnapshot.docs.map(doc => ({
        id: doc.id,
        ...doc.data(),
        userId: userDoc.id,
        createdAt: doc.data().createdAt?.toDate() || new Date(),
        completedAt: doc.data().completedAt?.toDate() || new Date()
      })) as RequestFormData[];

      allForms.push(...userForms);
    }

    return allForms.sort((a, b) => b.createdAt.getTime() - a.createdAt.getTime());
  } catch (error) {
    console.error('Error fetching completed request forms: ', error);
    throw new Error('Failed to fetch completed request forms');
  }
};

// Backward compatibility alias
export const getCompletedIstemFormuByPatient = getCompletedRequestFormsByPatient;

/**
 * Update request form status to completed and add scan images
 */
export const completeRequestForm = async (
  formId: string,
  userId: string,
  scanImages: string[]
): Promise<void> => {
  try {
    const formRef = doc(db, 'users', userId, 'istem-formu', formId);

    await updateDoc(formRef, {
      status: 'completed',
      images: scanImages,
      completedAt: new Date()
    });
  } catch (error) {
    console.error('Error completing request form: ', error);
    throw new Error('Failed to complete request form');
  }
};

/**
 * Delete a request form
 */
export const deleteRequestForm = async (formId: string, userId: string): Promise<void> => {
  try {
    const docRef = doc(db, 'users', userId, 'istem-formu', formId);
    await deleteDoc(docRef);
  } catch (error) {
    console.error('Error deleting request form:', error);
    throw new Error('Failed to delete request form');
  }
};

// Backward compatibility aliases
export const completeIstemFormu = completeRequestForm;
export const deleteIstemFormu = deleteRequestForm;