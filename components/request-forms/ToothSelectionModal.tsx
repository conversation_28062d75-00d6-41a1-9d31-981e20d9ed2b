'use client';

import { useState } from 'react';
import { X, Check } from 'lucide-react';
import FullTeethSet from '../dentoui/FullTeethSet';

interface ToothSelectionModalProps {
  isOpen: boolean;
  onClose: () => void;
  onSelectTooth: (teeth: string[]) => void;
}

export default function ToothSelectionModal({ isOpen, onClose, onSelectTooth }: ToothSelectionModalProps) {
  const [selectedTeeth, setSelectedTeeth] = useState<string[]>([]);

  if (!isOpen) return null;

  const handleTeethSelect = (toothId: string) => {
    setSelectedTeeth(prev => {
      if (prev.includes(toothId)) {
        return prev.filter(id => id !== toothId);
      } else {
        return [...prev, toothId];
      }
    });
  };

  const handleConfirmSelection = () => {
    onSelectTooth(selectedTeeth);
    onClose();
    setSelectedTeeth([]);
  };

  const handleClearSelection = () => {
    setSelectedTeeth([]);
  };

  return (
    <div className="fixed inset-0 z-50 overflow-y-auto animate-fadeIn">
      <div className="fixed inset-0 bg-black/50 backdrop-blur-sm transition-opacity animate-fadeIn" onClick={onClose} />
      <div className="flex min-h-full items-center justify-center p-4">
        <div className="relative w-full max-w-4xl bg-white rounded-2xl shadow-2xl transform transition-all animate-slideUp">
          <div className="bg-gradient-to-r from-blue-500 to-blue-600 rounded-t-2xl px-8 py-6">
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-4">
                <h2 className="text-2xl font-bold text-white">Diş Seçimi</h2>
                {selectedTeeth.length > 0 && (
                  <span className="bg-white/20 px-3 py-1 rounded-full text-white text-sm font-medium">
                    {selectedTeeth.length} diş seçildi
                  </span>
                )}
              </div>
              <button
                onClick={onClose}
                className="w-10 h-10 bg-white/20 hover:bg-white/30 rounded-xl flex items-center justify-center transition-colors cursor-pointer hover:scale-110 transform"
              >
                <X className="w-5 h-5 text-white" />
              </button>
            </div>
          </div>
          
          <div className="p-8">
            <div className="text-center mb-6">
              <p className="text-gray-600 mb-4">
                Tedavi görmesi gereken dişleri seçin
              </p>
              
                <div className="bg-gray-50 rounded-xl p-24 mb-6">
                 <FullTeethSet
                   selectedTeeth={selectedTeeth}
                   onTeethSelect={handleTeethSelect}
                   className="transform scale-175"
                 />
               </div>
              
              <div className="text-sm text-gray-500 mb-4">
                {selectedTeeth.length === 0 
                  ? "Henüz diş seçilmedi"
                  : `${selectedTeeth.length} diş seçildi - Birden fazla diş seçebilirsiniz`
                }
              </div>
            </div>

            <div className="flex justify-between space-x-3">
              <button
                onClick={handleClearSelection}
                disabled={selectedTeeth.length === 0}
                className="flex-1 px-6 py-3 border border-gray-300 rounded-xl text-gray-700 font-medium hover:bg-gray-50 transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
              >
                Seçimi Temizle
              </button>
              
              <button
                onClick={handleConfirmSelection}
                disabled={selectedTeeth.length === 0}
                className="flex-1 px-6 py-3 bg-blue-500 hover:bg-blue-600 text-white rounded-xl font-medium transition-colors disabled:opacity-50 disabled:cursor-not-allowed flex items-center justify-center space-x-2"
              >
                <Check className="w-5 h-5" />
                <span>Seçimi Onayla</span>
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
