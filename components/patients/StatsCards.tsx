'use client';

import { Patient } from '@/types';
import { Users, UserPlus, Calendar, CheckSquare } from 'lucide-react';

interface StatsCardsProps {
  patients: Patient[];
  totalPatients: number;
}

export default function StatsCards({ patients, totalPatients }: StatsCardsProps) {
  const newThisMonth = patients.filter(p => new Date(p.createdAt).getMonth() === new Date().getMonth()).length;

  return (
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
      {/* Toplam Hasta */}
      <div className="bg-gradient-to-br from-blue-500 to-blue-600 rounded-2xl shadow-lg p-6 flex items-center justify-between text-white hover:shadow-xl transition-shadow duration-300">
        <div>
          <p className="text-sm font-medium text-blue-100 uppercase tracking-wider">Toplam Hasta</p>
          <p className="text-3xl font-bold">{totalPatients}</p>
        </div>
        <div className="bg-white/20 rounded-xl p-3">
          <Users className="w-6 h-6" />
        </div>
      </div>

      {/* Aktif Hasta */}
      <div className="bg-gradient-to-br from-emerald-500 to-emerald-600 rounded-2xl shadow-lg p-6 flex items-center justify-between text-white hover:shadow-xl transition-shadow duration-300">
        <div>
          <p className="text-sm font-medium text-emerald-100 uppercase tracking-wider">Aktif Hasta</p>
          <p className="text-3xl font-bold">{totalPatients}</p>
        </div>
        <div className="bg-white/20 rounded-xl p-3">
          <CheckSquare className="w-6 h-6" />
        </div>
      </div>

      {/* Bu Ayki Yeni Hastalar */}
      <div className="bg-gradient-to-br from-purple-500 to-purple-600 rounded-2xl shadow-lg p-6 flex items-center justify-between text-white hover:shadow-xl transition-shadow duration-300">
        <div>
          <p className="text-sm font-medium text-purple-100 uppercase tracking-wider">Bu Ay Yeni</p>
          <p className="text-3xl font-bold">{newThisMonth}</p>
        </div>
        <div className="bg-white/20 rounded-xl p-3">
          <UserPlus className="w-6 h-6" />
        </div>
      </div>

      {/* Bekleyen Randevular (Örnek) */}
      <div className="bg-gradient-to-br from-orange-500 to-orange-600 rounded-2xl shadow-lg p-6 flex items-center justify-between text-white hover:shadow-xl transition-shadow duration-300">
        <div>
          <p className="text-sm font-medium text-orange-100 uppercase tracking-wider">Bekleyen Randevu</p>
          <p className="text-3xl font-bold">12</p>
        </div>
        <div className="bg-white/20 rounded-xl p-3">
          <Calendar className="w-6 h-6" />
        </div>
      </div>
    </div>
  );
} 