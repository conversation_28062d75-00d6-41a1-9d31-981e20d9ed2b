# Dento - Dental Practice Management System

A comprehensive dental practice management system built with modern web technologies, designed to streamline patient management, request forms, and administrative tasks for dental clinics.

## 🚀 Features

- **Patient Management**: Complete patient records with demographics, medical history, and treatment tracking
- **Request Forms (İstem Formu)**: Digital dental procedure request forms with image uploads and tooth selection
- **Admin Dashboard**: User management, approval workflows, and system administration
- **Authentication**: Secure user registration, login, and role-based access control
- **Reports & Analytics**: Treatment statistics and practice insights
- **Multi-language Support**: Turkish and English interface support
- **Real-time Updates**: Live data synchronization across all users

## 🛠️ Tech Stack

### Frontend
- **Next.js 15** - React framework with App Router
- **TypeScript** - Type-safe development
- **Tailwind CSS** - Utility-first CSS framework
- **DaisyUI** - Component library for Tailwind CSS
- **Shadcn/ui** - Modern React component library
- **Framer Motion** - Animation library
- **Zustand** - State management
- **React Hook Form** - Form handling
- **Zod** - Schema validation

### Backend & Services
- **Firebase Authentication** - User authentication and authorization
- **Firestore** - NoSQL database
- **Firebase Functions** - Serverless backend functions
- **Firebase Storage** - File storage for images and documents
- **Brevo (Sendinblue)** - Email service integration

### Development Tools
- **ESLint** - Code linting
- **TypeScript** - Static type checking
- **Firebase Emulators** - Local development environment

## 📁 Project Structure

```
dento/
├── app/                    # Next.js App Router pages
│   ├── auth/              # Authentication pages
│   ├── dashboard/         # Main application dashboard
│   └── layout.tsx         # Root layout
├── components/            # React components
│   ├── admin/            # Admin-specific components
│   ├── auth/             # Authentication components
│   ├── dentoui/          # Custom design system
│   ├── hastalar/         # Patient management components
│   ├── istem-formu/      # Request form components
│   ├── settings/         # Settings components
│   └── ui/               # Shadcn/ui components
├── lib/                   # Shared utilities and services
│   ├── contexts/         # React contexts
│   ├── hooks/            # Custom React hooks
│   ├── schemas/          # Zod validation schemas
│   ├── services/         # API services
│   ├── stores/           # Zustand stores
│   └── utils/            # Utility functions (organized by category)
├── types/                 # TypeScript type definitions
├── functions/             # Firebase Cloud Functions
│   └── src/              # Function source code
├── public/               # Static assets
├── docs/                 # Documentation
└── scripts/              # Utility scripts
```

## 🚀 Getting Started

### Prerequisites

- Node.js 18+
- npm or yarn
- Firebase CLI
- Firebase project setup

### Installation

1. **Clone the repository**
   ```bash
   git clone <repository-url>
   cd dento
   ```

2. **Install dependencies**
   ```bash
   npm install
   ```

3. **Install Firebase Functions dependencies**
   ```bash
   cd functions
   npm install
   cd ..
   ```

4. **Environment Setup**
   ```bash
   cp .env.example .env.local
   ```
   Fill in your Firebase configuration values in `.env.local`

5. **Firebase Setup**
   ```bash
   firebase login
   firebase use <your-project-id>
   ```

### Development

1. **Start the development server**
   ```bash
   npm run dev
   ```

2. **Start Firebase emulators** (in a separate terminal)
   ```bash
   firebase emulators:start
   ```

3. **Open your browser**
   Navigate to [http://localhost:3000](http://localhost:3000)

### Building for Production

```bash
npm run build
npm run start
```

## 🔧 Configuration

### Firebase Setup
See the documentation files in the `docs/` directory:
- `docs/ADMIN_SETUP.md` - Admin user configuration
- `docs/BREVO_SETUP.md` - Email service setup
- `docs/SECURITY_MIGRATION_GUIDE.md` - Security configuration

### Environment Variables
Copy `.env.example` to `.env.local` and configure:
- Firebase configuration
- API keys
- Service URLs

## 📚 Documentation

- [Admin Setup Guide](docs/ADMIN_SETUP.md)
- [Email Service Configuration](docs/BREVO_SETUP.md)
- [Security Migration Guide](docs/SECURITY_MIGRATION_GUIDE.md)

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch (`git checkout -b feature/amazing-feature`)
3. Commit your changes (`git commit -m 'Add some amazing feature'`)
4. Push to the branch (`git push origin feature/amazing-feature`)
5. Open a Pull Request

## 📄 License

This project is private and proprietary.

## 🆘 Support

For support and questions, please contact the development team.
