# Development Guide

This guide covers the development setup, workflow, and best practices for the Dento project.

## 🛠️ Development Environment Setup

### Prerequisites
- **Node.js**: Version 18 or higher
- **npm**: Comes with Node.js
- **Firebase CLI**: `npm install -g firebase-tools`
- **Git**: For version control

### Initial Setup

1. **Clone the repository**
   ```bash
   git clone <repository-url>
   cd dento
   ```

2. **Install dependencies**
   ```bash
   # Install main project dependencies
   npm install
   
   # Install Firebase Functions dependencies
   cd functions
   npm install
   cd ..
   ```

3. **Environment Configuration**
   ```bash
   cp .env.example .env.local
   ```
   
   Fill in the required environment variables:
   - Firebase configuration
   - API keys
   - Service URLs

4. **Firebase Setup**
   ```bash
   firebase login
   firebase use <your-project-id>
   ```

### Development Workflow

1. **Start Development Server**
   ```bash
   npm run dev
   ```
   This starts the Next.js development server with Turbopack.

2. **Start Firebase Emulators** (in a separate terminal)
   ```bash
   firebase emulators:start
   ```
   This provides local Firebase services for development.

3. **Access the Application**
   - Frontend: http://localhost:3000
   - Firebase Emulator UI: http://localhost:4000

## 🏗️ Project Architecture

### Frontend Architecture
- **Framework**: Next.js 15 with App Router
- **Language**: TypeScript
- **Styling**: Tailwind CSS + DaisyUI
- **State Management**: Zustand
- **Form Handling**: React Hook Form + Zod
- **UI Components**: Shadcn/ui + Custom components

### Backend Architecture
- **Authentication**: Firebase Auth
- **Database**: Firestore
- **Functions**: Firebase Cloud Functions
- **Storage**: Firebase Storage
- **Email**: Brevo (Sendinblue)

### Data Flow
```
User Interface → React Components → Services → Firebase → Database
                     ↓
                State Management (Zustand)
                     ↓
                Real-time Updates
```

## 📁 Directory Structure Explained

```
dento/
├── app/                    # Next.js App Router
│   ├── auth/              # Authentication pages
│   ├── dashboard/         # Main application
│   ├── layout.tsx         # Root layout
│   └── page.tsx           # Home page
├── components/            # React components
│   ├── ui/               # Shadcn/ui components
│   ├── dentoui/          # Custom design system
│   └── [feature]/        # Feature-specific components
├── lib/                   # Shared utilities
│   ├── contexts/         # React contexts
│   ├── hooks/            # Custom hooks
│   ├── services/         # API services
│   ├── stores/           # Zustand stores
│   ├── schemas/          # Zod schemas
│   └── utils/            # Utility functions (organized by category)
├── types/                 # TypeScript type definitions
├── functions/             # Firebase Cloud Functions
├── tests/                 # Test files
├── docs/                  # Documentation
└── public/               # Static assets
```

## 🔧 Development Tools

### Code Quality
- **ESLint**: Code linting
- **TypeScript**: Static type checking
- **Prettier**: Code formatting (via ESLint)

### Testing
- **Jest**: Test runner
- **React Testing Library**: Component testing
- **Coverage**: Minimum 70% coverage required

### Build Tools
- **Next.js**: Framework and build tool
- **Turbopack**: Fast development bundler
- **TypeScript**: Compilation

## 🚀 Common Development Tasks

### Adding a New Component
1. Create component file in appropriate directory
2. Export from index file if needed
3. Add TypeScript interfaces
4. Write tests
5. Update documentation

### Adding a New Service
1. Create service file in `lib/services/`
2. Define TypeScript interfaces
3. Implement error handling
4. Add tests
5. Update service exports

### Database Schema Changes
1. Update Firestore rules if needed
2. Update TypeScript types
3. Create migration scripts if necessary
4. Test with emulators
5. Document changes

### Adding New Dependencies
```bash
# Production dependency
npm install package-name

# Development dependency
npm install -D package-name
```

## 🐛 Debugging

### Frontend Debugging
- Use React Developer Tools
- Check browser console
- Use Next.js built-in debugging
- Inspect network requests

### Backend Debugging
- Check Firebase Functions logs
- Use Firebase Emulator UI
- Monitor Firestore operations
- Check authentication flow

### Common Issues
1. **Firebase connection issues**: Check environment variables
2. **Build errors**: Clear `.next` folder and rebuild
3. **Type errors**: Update TypeScript definitions
4. **Test failures**: Check mock configurations

## 📊 Performance Considerations

### Frontend Performance
- Use Next.js Image optimization
- Implement proper loading states
- Optimize bundle size
- Use React.memo for expensive components

### Backend Performance
- Optimize Firestore queries
- Use proper indexing
- Implement caching where appropriate
- Monitor function execution time

## 🔒 Security Best Practices

### Frontend Security
- Validate all user inputs
- Use proper authentication checks
- Sanitize data before display
- Implement proper error handling

### Backend Security
- Follow Firebase security rules
- Validate data on server side
- Use proper authentication
- Implement rate limiting

## 📝 Code Style Guidelines

### TypeScript
- Use strict mode
- Define proper interfaces
- Avoid `any` type
- Use proper generics

### React
- Use functional components
- Implement proper error boundaries
- Use proper key props
- Follow hooks rules

### CSS
- Use Tailwind utility classes
- Follow mobile-first approach
- Use semantic class names
- Implement proper responsive design

## 🧪 Testing Strategy

### Unit Tests
- Test individual components
- Test utility functions
- Mock external dependencies
- Aim for high coverage

### Integration Tests
- Test component interactions
- Test service integrations
- Test user workflows
- Use realistic data

### E2E Tests (Future)
- Test complete user journeys
- Test critical business flows
- Use production-like environment

## 🚀 Deployment

### Development Deployment
```bash
# Deploy to development environment
firebase use dev
firebase deploy
```

### Production Deployment
```bash
# Deploy to production
firebase use production
npm run build
firebase deploy
```

## 📚 Additional Resources

- [Next.js Documentation](https://nextjs.org/docs)
- [Firebase Documentation](https://firebase.google.com/docs)
- [Tailwind CSS Documentation](https://tailwindcss.com/docs)
- [TypeScript Documentation](https://www.typescriptlang.org/docs)

## 🆘 Getting Help

1. Check this documentation
2. Search existing issues
3. Check Firebase documentation
4. Ask team members
5. Create new issue if needed
