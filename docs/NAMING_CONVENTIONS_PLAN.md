# Naming Conventions Standardization Plan

This document outlines the current naming inconsistencies in the Dento project and provides a comprehensive plan for standardization.

## 🔍 Current State Analysis

### Mixed Language Usage
The project currently uses a mix of Turkish and English naming conventions:

#### Turkish Names (Need Standardization)
- **Directories:**
  - `components/hastalar/` → should be `components/patients/`
  - `components/istem-formu/` → should be `components/request-forms/`

- **Files:**
  - `IstemFormuData` interface → should be `RequestFormData`
  - `istemFormuService.ts` → should be `requestFormService.ts`
  - Various component files with Turkish names

- **Database Collections:**
  - `istem-formu` collection → should be `request-forms`

#### Mixed Casing Patterns
- **kebab-case:** `istem-formu`, `hastalar` (directories)
- **PascalCase:** `IstemFormuData`, `AddPatientModal` (components/types)
- **camelCase:** `istemFormuService`, `patientService` (services)

## 🎯 Standardization Goals

1. **Language Consistency:** Use English for all technical naming
2. **Case Consistency:** Follow established conventions per file type
3. **Semantic Clarity:** Use descriptive, self-documenting names
4. **International Compatibility:** Enable easier collaboration with international developers

## 📋 Proposed Naming Conventions

### Directory Structure
```
components/
├── patients/              # (was hastalar)
├── request-forms/         # (was istem-formu)
├── admin/                 # ✅ already good
├── auth/                  # ✅ already good
├── settings/              # ✅ already good
└── ui/                    # ✅ already good
```

### File Naming Conventions

#### Components (PascalCase)
- `PatientTable.tsx` (was `PatientsTable.tsx` - keep as is)
- `RequestFormModal.tsx` (was `IstemFormuDetailsModal.tsx`)
- `RequestFormTable.tsx` (was `IstemFormuTable.tsx`)
- `AddPatientModal.tsx` ✅ already good

#### Services (camelCase)
- `requestFormService.ts` (was `istemFormuService.ts`)
- `patientService.ts` ✅ already good
- `userService.ts` ✅ already good

#### Types (PascalCase)
- `RequestFormData` (was `IstemFormuData`)
- `Patient` ✅ already good
- `User` ✅ already good

### Database Collections
- `request-forms` (was `istem-formu`)
- `patients` ✅ already good
- `users` ✅ already good

## 🚀 Migration Plan

### Phase 1: Type Definitions (Low Risk)
1. Update type names in `types/index.ts`
2. Create type aliases for backward compatibility
3. Update imports gradually

### Phase 2: Service Layer (Medium Risk)
1. Rename service files
2. Update function names within services
3. Update imports across the application
4. Maintain backward compatibility during transition

### Phase 3: Component Structure (Medium Risk)
1. Rename component directories
2. Update component file names
3. Update all imports and references
4. Update routing if affected

### Phase 4: Database Schema (High Risk)
1. Create migration scripts for Firestore collections
2. Update security rules
3. Update all database queries
4. Test thoroughly in development environment

## 📝 Detailed Migration Steps

### Step 1: Create Type Aliases (Immediate)
```typescript
// In types/index.ts - Add backward compatibility
export interface RequestFormData {
  // ... existing IstemFormuData properties
}

// Backward compatibility
export type IstemFormuData = RequestFormData;
```

### Step 2: Update Component Directory Structure
```bash
# Rename directories
mv components/hastalar components/patients
mv components/istem-formu components/request-forms

# Update all import statements
# Update routing configurations
```

### Step 3: Service Layer Updates
```typescript
// Rename files
mv lib/services/istemFormuService.ts lib/services/requestFormService.ts

// Update function names
export const saveRequestForm = async (data: RequestFormData) => {
  // Implementation
}

// Keep aliases for backward compatibility
export const saveIstemFormu = saveRequestForm;
```

### Step 4: Database Migration
```typescript
// Migration script example
const migrateCollectionNames = async () => {
  // Copy data from old collection to new collection
  // Update security rules
  // Update indexes
  // Remove old collections after verification
}
```

## ⚠️ Risk Assessment

### Low Risk Changes
- Type name updates with aliases
- Internal function renames with aliases
- Documentation updates

### Medium Risk Changes
- Component file and directory renames
- Service file renames
- Import path updates

### High Risk Changes
- Database collection renames
- Security rule updates
- Production data migration

## 🛡️ Mitigation Strategies

### Backward Compatibility
- Maintain type aliases during transition period
- Keep old function names as aliases
- Gradual migration approach

### Testing Strategy
- Comprehensive unit test updates
- Integration testing for renamed components
- Database migration testing in development
- Rollback procedures for each phase

### Documentation
- Update all documentation simultaneously
- Maintain changelog of naming changes
- Create migration guide for team members

## 📅 Implementation Timeline

### Week 1: Preparation
- [ ] Create detailed file mapping
- [ ] Set up development branch
- [ ] Create backward compatibility aliases

### Week 2: Types and Services
- [ ] Update type definitions
- [ ] Rename service files
- [ ] Update service function names
- [ ] Update imports in components

### Week 3: Components
- [ ] Rename component directories
- [ ] Update component file names
- [ ] Update all import statements
- [ ] Update routing configurations

### Week 4: Database (Optional - Future Phase)
- [ ] Create migration scripts
- [ ] Test in development environment
- [ ] Update security rules
- [ ] Plan production migration

## 🔄 Rollback Plan

Each phase should have a clear rollback strategy:

1. **Git branches:** Separate branch for each phase
2. **Database backups:** Before any database changes
3. **Deployment rollback:** Ability to revert to previous version
4. **Documentation:** Clear rollback procedures

## 📊 Success Metrics

- [ ] All file names follow consistent conventions
- [ ] No broken imports or references
- [ ] All tests pass
- [ ] Documentation is updated
- [ ] Team can navigate codebase more easily
- [ ] International developers can contribute more effectively

## 🤝 Team Coordination

### Communication Plan
- Announce changes before implementation
- Provide migration guide for team members
- Schedule team review sessions
- Document lessons learned

### Training
- Update onboarding documentation
- Create naming convention guide
- Establish code review guidelines

## 📚 Future Considerations

### Internationalization (i18n)
- Separate display text from technical naming
- Use translation keys for user-facing text
- Maintain Turkish language support in UI

### API Consistency
- Align API endpoints with new naming
- Update API documentation
- Maintain backward compatibility for external integrations

This plan ensures a systematic approach to standardizing naming conventions while minimizing risks and maintaining system stability.
